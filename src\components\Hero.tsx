import { Button } from "@/components/ui/button";
import heroImage from "@/assets/hero-sports.jpg";

const Hero = () => {
  return (
    <section className="relative overflow-hidden bg-gradient-subtle">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-[600px] py-12 lg:py-20">
          {/* Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight">
                Gear Up for
                <span className="block bg-gradient-hero bg-clip-text text-transparent">
                  Greatness
                </span>
              </h1>
              <p className="text-lg sm:text-xl text-muted-foreground max-w-lg">
                Premium sports wear and equipment for athletes who demand excellence. 
                From weekend warriors to professional competitors.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <Button variant="hero" size="lg" className="w-full sm:w-auto">
                Shop Collection
              </Button>
              <Button variant="outline" size="lg" className="w-full sm:w-auto">
                View Catalog
              </Button>
            </div>

            <div className="flex items-center space-x-8 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-accent rounded-full"></div>
                <span>Free Shipping</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-accent rounded-full"></div>
                <span>30-Day Returns</span>
              </div>
            </div>
          </div>

          {/* Hero Image */}
          <div className="relative lg:order-last">
            <div className="aspect-square lg:aspect-[4/3] relative overflow-hidden rounded-2xl shadow-athletic">
              <img 
                src={heroImage} 
                alt="Premium sports equipment and athletic wear" 
                className="object-cover w-full h-full transform hover:scale-105 transition-transform duration-700"
              />
              <div className="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;