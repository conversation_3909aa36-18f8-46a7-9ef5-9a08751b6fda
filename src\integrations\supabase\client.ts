// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://lzdqxytrxpzscuceczrs.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx6ZHF4eXRyeHB6c2N1Y2VjenJzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQyMDQ2MDEsImV4cCI6MjA2OTc4MDYwMX0.Pf5BWC8Vk-aOAo1Gfka1DblfdWoqIRrshoUgH8rhT2A";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});